{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/services/order.service.js"], "sourcesContent": ["export default class OrderService {\r\n    static async getOrders() {\r\n        try {\r\n            const response = await fetch(\"/orders.json\")\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`)\r\n            }\r\n            const orders = await response.json()\r\n            return orders\r\n        } catch (error) {\r\n            console.error('Ошибка при загрузке заказов:', error)\r\n            throw error\r\n        }\r\n    }\r\n\r\n    static async getOrdersByStatus(status) {\r\n        try {\r\n            const response = await fetch(\"/orders.json\")\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`)\r\n            }\r\n            const orders = await response.json()\r\n            return orders.filter(order => order.status === status)\r\n        } catch (error) {\r\n            console.error('Ошибка при загрузке заказов по статусу:', error)\r\n            throw error\r\n        }\r\n    }\r\n\r\n    static async getOrderById(id) {\r\n        try {\r\n            const response = await fetch(\"/orders.json\")\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`)\r\n            }\r\n            const orders = await response.json()\r\n            return orders.find(order => order.id === id)\r\n        } catch (error) {\r\n            console.error('Ошибка при загрузке заказа по ID:', error)\r\n            throw error\r\n        }\r\n    }\r\n}"], "names": [], "mappings": ";;;AAAe,MAAM;IACjB,aAAa,YAAY;QACrB,IAAI;YACA,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACV;IACJ;IAEA,aAAa,kBAAkB,MAAM,EAAE;QACnC,IAAI;YACA,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACnD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACV;IACJ;IAEA,aAAa,aAAa,EAAE,EAAE;QAC1B,IAAI;YACA,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC7C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACV;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/contexts/OrdersContext.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { createContext, useState, useContext, useEffect } from \"react\";\r\nimport OrderService from \"@/services/order.service\";\r\n\r\nexport const OrdersContext = createContext();\r\n\r\nexport const OrdersProvider = ({ children }) => {\r\n    const [orders, setOrders] = useState([])\r\n    const [loading, setLoading] = useState(false)\r\n    const [error, setError] = useState(null)\r\n\r\n    // Загружаем все заказы при инициализации\r\n    useEffect(() => {\r\n        loadOrders()\r\n    }, [])\r\n\r\n    async function loadOrders() {\r\n        setLoading(true)\r\n        setError(null)\r\n        try {\r\n            const ordersData = await OrderService.getOrders()\r\n            setOrders(ordersData)\r\n        } catch (error) {\r\n            console.error('Ошибка при загрузке заказов:', error)\r\n            setError(error.message)\r\n        } finally {\r\n            setLoading(false)\r\n        }\r\n    }\r\n\r\n    async function getOrders() {\r\n        try {\r\n            return await OrderService.getOrders()\r\n        } catch (error) {\r\n            console.error('Ошибка при получении заказов:', error)\r\n            setError(error.message)\r\n            return []\r\n        }\r\n    }\r\n\r\n    async function getOrdersByStatus(status) {\r\n        try {\r\n            return await OrderService.getOrdersByStatus(status)\r\n        } catch (error) {\r\n            console.error('Ошибка при получении заказов по статусу:', error)\r\n            setError(error.message)\r\n            return []\r\n        }\r\n    }\r\n\r\n    // Синхронная функция для получения заказов по статусу из уже загруженных данных\r\n    function getOrdersByStatusSync(status) {\r\n        return orders.filter(order => order.status === status)\r\n    }\r\n\r\n    async function getOrderById(id) {\r\n        try {\r\n            return await OrderService.getOrderById(id)\r\n        } catch (error) {\r\n            console.error('Ошибка при получении заказа по ID:', error)\r\n            setError(error.message)\r\n            return null\r\n        }\r\n    }\r\n\r\n    return (\r\n        <OrdersContext.Provider value={{\r\n            orders,\r\n            setOrders,\r\n            loading,\r\n            error,\r\n            getOrders,\r\n            getOrdersByStatus,\r\n            getOrdersByStatusSync,\r\n            getOrderById,\r\n            loadOrders\r\n        }}>\r\n            {children}\r\n        </OrdersContext.Provider>\r\n    );\r\n};\r\n\r\nexport function useOrders() {\r\n    return useContext(OrdersContext)\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAKO,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAElC,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACJ,GAAG,EAAE;IAEL,eAAe;QACX,WAAW;QACX,SAAS;QACT,IAAI;YACA,MAAM,aAAa,MAAM,mIAAA,CAAA,UAAY,CAAC,SAAS;YAC/C,UAAU;QACd,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,MAAM,OAAO;QAC1B,SAAU;YACN,WAAW;QACf;IACJ;IAEA,eAAe;QACX,IAAI;YACA,OAAO,MAAM,mIAAA,CAAA,UAAY,CAAC,SAAS;QACvC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,MAAM,OAAO;YACtB,OAAO,EAAE;QACb;IACJ;IAEA,eAAe,kBAAkB,MAAM;QACnC,IAAI;YACA,OAAO,MAAM,mIAAA,CAAA,UAAY,CAAC,iBAAiB,CAAC;QAChD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,SAAS,MAAM,OAAO;YACtB,OAAO,EAAE;QACb;IACJ;IAEA,gFAAgF;IAChF,SAAS,sBAAsB,MAAM;QACjC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IACnD;IAEA,eAAe,aAAa,EAAE;QAC1B,IAAI;YACA,OAAO,MAAM,mIAAA,CAAA,UAAY,CAAC,YAAY,CAAC;QAC3C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,SAAS,MAAM,OAAO;YACtB,OAAO;QACX;IACJ;IAEA,qBACI,8OAAC,cAAc,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;kBACK;;;;;;AAGb;AAEO,SAAS;IACZ,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/contexts/MainProvider.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { OrdersProvider } from \"./OrdersContext\";\r\n\r\nexport const MainProvider = ({ children }) => {\r\n    return (\r\n        <OrdersProvider>\r\n            {children}\r\n        </OrdersProvider>\r\n    );\r\n};"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE;IACrC,qBACI,8OAAC,iIAAA,CAAA,iBAAc;kBACV;;;;;;AAGb", "debugId": null}}]}