'use client'

import { useState, useEffect } from "react";
import { Section } from "@/components/layout";
import OrderReviewCard from "@/components/shared/OrderReviewCard";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, Truck, Package } from "lucide-react";
import { useOrders } from "@/contexts/OrdersContext";

const statusSections = [
  {
    id: "item-1",
    title: "Новые заказы",
    status: "new",
    count: 3,
    icon: Clock,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200"
  },
  {
    id: "item-2",
    title: "Подтверждены",
    status: "confirmed",
    count: 10,
    icon: CheckCircle,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    id: "item-3",
    title: "Готовы",
    status: "completed",
    count: 2,
    icon: Package,
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  },
  {
    id: "item-4",
    title: "В пути",
    status: "delivery",
    count: 80,
    icon: Truck,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  }
];

export default function Home() {
  const { orders, loading, error, getOrdersByStatusSync } = useOrders()
  const [sectionOrders, setSectionOrders] = useState({})

  // Обновляем заказы для каждой секции при изменении общего списка заказов
  useEffect(() => {
    if (orders.length > 0) {
      const newSectionOrders = {}
      statusSections.forEach(section => {
        newSectionOrders[section.status] = getOrdersByStatusSync(section.status)
      })
      setSectionOrders(newSectionOrders)
    }
  }, [orders, getOrdersByStatusSync])

  // Обновляем счетчики в секциях на основе реальных данных
  const updatedStatusSections = statusSections.map(section => ({
    ...section,
    count: sectionOrders[section.status]?.length || 0
  }))

  if (loading) {
    return (
      <Section spacing="sm">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">Загрузка заказов...</div>
        </div>
      </Section>
    )
  }

  if (error) {
    return (
      <Section spacing="sm">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">Ошибка загрузки заказов: {error}</div>
        </div>
      </Section>
    )
  }

  return (
    <Section spacing="sm">
      <Accordion
        type="multiple"
        className="space-y-3 bg-gray-50 rounded-xl p-4"
      >
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Заказы</h1>
        {updatedStatusSections.map((section) => {
          const IconComponent = section.icon;
          const sectionOrdersList = sectionOrders[section.status] || []

          return (
            <AccordionItem
              key={section.id}
              value={section.id}
              className="bg-white rounded-xl border border-gray-100 overflow-hidden"
            >
              <AccordionTrigger className="text-lg font-semibold text-gray-900 hover:no-underline px-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <IconComponent className={`w-5 h-5 ${section.color}`} />
                    <span>{section.title}</span>
                  </div>
                  <Badge
                    variant="secondary"
                    className={`font-mono ${section.bgColor} ${section.color} border-0 ml-2`}
                  >
                    {section.count}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4">
                {sectionOrdersList.length > 0 ? (
                  sectionOrdersList.map(order => (
                    <OrderReviewCard key={order.id} order={order} />
                  ))
                ) : (
                  <div className="text-gray-500 text-center py-4">
                    Нет заказов с статусом "{section.title}"
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </Section>
  );
}
