{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderStatus.js"], "sourcesContent": ["/**\n * Enum для статусов заказов\n * Содержит все возможные статусы заказов и их строковые значения\n */\nexport const ORDER_STATUS = {\n  NEW: 'new',\n  CONFIRMED: 'confirmed', \n  COMPLETED: 'completed',\n  DELIVERY: 'delivery',\n  CANCELLED: 'cancelled'\n};\n\n/**\n * Массив статусов в порядке их следования в жизненном цикле заказа\n */\nexport const STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.CANCELLED\n];\n\n/**\n * Проверяет, является ли переданное значение валидным статусом заказа\n * @param {string} status - Статус для проверки\n * @returns {boolean} - true если статус валидный\n */\nexport const isValidStatus = (status) => {\n  return Object.values(ORDER_STATUS).includes(status);\n};\n\n/**\n * Получает все возможные статусы заказов\n * @returns {string[]} - Массив всех статусов\n */\nexport const getAllStatuses = () => {\n  return Object.values(ORDER_STATUS);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AACM,MAAM,eAAe;IAC1B,KAAK;IACL,WAAW;IACX,WAAW;IACX,UAAU;IACV,WAAW;AACb;AAKO,MAAM,eAAe;IAC1B,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAOM,MAAM,gBAAgB,CAAC;IAC5B,OAAO,OAAO,MAAM,CAAC,cAAc,QAAQ,CAAC;AAC9C;AAMO,MAAM,iBAAiB;IAC5B,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock } from \"lucide-react\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        [ORDER_STATUS.NEW]: {\r\n            icon: Clock,\r\n            label: \"Новый\",\r\n            className: \"bg-orange-50 text-orange-600 border-orange-200\"\r\n        },\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            className: \"bg-blue-50 text-blue-600 border-blue-200\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            className: \"bg-green-50 text-green-600 border-green-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В пути\",\r\n            className: \"bg-purple-50 text-purple-600 border-purple-200\"\r\n        },\r\n        [ORDER_STATUS.CANCELLED]: {\r\n            icon: <PERSON><PERSON><PERSON><PERSON>,\r\n            label: \"Отменен\",\r\n            className: \"bg-red-50 text-red-600 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs[ORDER_STATUS.NEW];\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,CAAC,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderReviewCard.jsx"], "sourcesContent": ["import Link from \"next/link\"\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderReviewCard({ order }) {\r\n    const statusConfig = getStatusConfig(order.status)\r\n    const StatusIcon = statusConfig.icon\r\n\r\n    return (\r\n        <Link href={`/orders/${order.id}`}>\r\n            <div className=\"bg-gray-50 rounded-xl p-4 mt-4 hover:bg-gray-100 transition-colors border border-gray-200\">\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                    <h5 className=\"text-base font-semibold text-gray-900\">\r\n                        Заказ #{order.id}\r\n                        <span className=\"text-sm text-gray-500 font-normal pl-2\">{order.delivery_type}</span>\r\n                    </h5>\r\n                    <div className={cn(\r\n                        \"inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium border\",\r\n                        statusConfig.className\r\n                    )}>\r\n                        <StatusIcon className=\"w-3 h-3\" />\r\n                        {statusConfig.label}\r\n                    </div>\r\n                </div>\r\n                <div className=\"space-y-2 text-sm text-gray-600\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <span>Гость: {order.user.first_name}</span>\r\n                        <span className=\"font-medium text-gray-900\">{order.amount} ₽</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </Link>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS,gBAAgB,EAAE,KAAK,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACI,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;;gCAAwC;gCAC1C,MAAM,EAAE;8CAChB,8OAAC;oCAAK,WAAU;8CAA0C,MAAM,aAAa;;;;;;;;;;;;sCAEjF,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,oFACA,aAAa,SAAS;;8CAEtB,8OAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;8BAG3B,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;;oCAAK;oCAAQ,MAAM,IAAI,CAAC,UAAU;;;;;;;0CACnC,8OAAC;gCAAK,WAAU;;oCAA6B,MAAM,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlF", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/accordion.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/accordion.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEf", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardHeader({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardTitle({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardDescription({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardAction({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardContent({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (<div data-slot=\"card-content\" className={cn(\"px-6\", className)} {...props} />);\r\n}\r\n\r\nfunction CardFooter({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;;AAEA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBAAQ,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AACnF;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAEf", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderConstants.js"], "sourcesContent": ["/**\n * Константы для типов доставки\n */\nexport const DELIVERY_TYPE = {\n  DELIVERY: 'Доставка',\n  PICKUP: 'Самовывоз'\n};\n\n/**\n * Константы для систем оплаты\n */\nexport const PAYMENT_SYSTEM = {\n  BANK_TRANSFER: 'banktransfer',\n  CASH: 'cash',\n  CARD: 'card'\n};\n\n/**\n * Лейблы для систем оплаты\n */\nexport const PAYMENT_LABELS = {\n  [PAYMENT_SYSTEM.BANK_TRANSFER]: 'Банковский перевод',\n  [PAYMENT_SYSTEM.CASH]: 'Наличные',\n  [PAYMENT_SYSTEM.CARD]: 'Банковская карта'\n};\n\n/**\n * Получает лейбл для системы оплаты\n * @param {string} system - Система оплаты\n * @returns {string} - Лейбл системы оплаты\n */\nexport function getPaymentLabel(system) {\n  return PAYMENT_LABELS[system] || system;\n}\n\n/**\n * Проверяет, является ли значение валидным типом доставки\n * @param {string} deliveryType - Тип доставки для проверки\n * @returns {boolean}\n */\nexport function isValidDeliveryType(deliveryType) {\n  return Object.values(DELIVERY_TYPE).includes(deliveryType);\n}\n\n/**\n * Проверяет, является ли значение валидной системой оплаты\n * @param {string} paymentSystem - Система оплаты для проверки\n * @returns {boolean}\n */\nexport function isValidPaymentSystem(paymentSystem) {\n  return Object.values(PAYMENT_SYSTEM).includes(paymentSystem);\n}\n\n/**\n * Возвращает все доступные типы доставки\n * @returns {string[]}\n */\nexport function getAllDeliveryTypes() {\n  return Object.values(DELIVERY_TYPE);\n}\n\n/**\n * Возвращает все доступные системы оплаты\n * @returns {string[]}\n */\nexport function getAllPaymentSystems() {\n  return Object.values(PAYMENT_SYSTEM);\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;AACM,MAAM,gBAAgB;IAC3B,UAAU;IACV,QAAQ;AACV;AAKO,MAAM,iBAAiB;IAC5B,eAAe;IACf,MAAM;IACN,MAAM;AACR;AAKO,MAAM,iBAAiB;IAC5B,CAAC,eAAe,aAAa,CAAC,EAAE;IAChC,CAAC,eAAe,IAAI,CAAC,EAAE;IACvB,CAAC,eAAe,IAAI,CAAC,EAAE;AACzB;AAOO,SAAS,gBAAgB,MAAM;IACpC,OAAO,cAAc,CAAC,OAAO,IAAI;AACnC;AAOO,SAAS,oBAAoB,YAAY;IAC9C,OAAO,OAAO,MAAM,CAAC,eAAe,QAAQ,CAAC;AAC/C;AAOO,SAAS,qBAAqB,aAAa;IAChD,OAAO,OAAO,MAAM,CAAC,gBAAgB,QAAQ,CAAC;AAChD;AAMO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAMO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/models/Order.js"], "sourcesContent": ["import { ORDER_STATUS, isValidStatus } from '@/constants/orderStatus';\nimport { DELIVERY_TYPE, PAYMENT_SYSTEM, isValidDeliveryType, isValidPaymentSystem } from '@/constants/orderConstants';\n\n/**\n * Модель пользователя заказа\n */\nexport class OrderUser {\n  /**\n   * @param {Object} data - Данные пользователя\n   * @param {string} data.first_name - Имя пользователя\n   * @param {string} data.phone - Телефон пользователя\n   */\n  constructor(data = {}) {\n    this.first_name = data.first_name || '';\n    this.phone = data.phone || '';\n  }\n\n  /**\n   * Проверяет валидность данных пользователя\n   * @returns {boolean}\n   */\n  isValid() {\n    return this.first_name.trim() !== '' && this.phone.trim() !== '';\n  }\n\n  /**\n   * Возвращает полное имя пользователя\n   * @returns {string}\n   */\n  getDisplayName() {\n    return this.first_name.trim();\n  }\n\n  /**\n   * Форматирует телефон для отображения\n   * @returns {string}\n   */\n  getFormattedPhone() {\n    return this.phone;\n  }\n}\n\n/**\n * Модель блюда в заказе\n */\nexport class OrderMeal {\n  /**\n   * @param {Object} data - Данные блюда\n   * @param {string} data.id - Уникальный идентификатор блюда\n   * @param {string} data.name - Название блюда\n   * @param {number} data.quantity - Количество в граммах\n   * @param {number} data.amount - Стоимость\n   * @param {string} data.img - URL изображения\n   * @param {string} data.pack_m - Масса упаковки\n   * @param {string} data.price - Цена за единицу\n   * @param {string} data.unit - Единица измерения\n   * @param {string} data.portion - Размер порции\n   */\n  constructor(data = {}) {\n    this.id = data.id || '';\n    this.name = data.name || '';\n    this.quantity = Number(data.quantity) || 0;\n    this.amount = Number(data.amount) || 0;\n    this.img = data.img || '';\n    this.pack_m = data.pack_m || '';\n    this.price = data.price || '';\n    this.unit = data.unit || 'г';\n    this.portion = data.portion || '';\n  }\n\n  /**\n   * Проверяет валидность данных блюда\n   * @returns {boolean}\n   */\n  isValid() {\n    return this.id !== '' && this.name !== '' && this.quantity > 0 && this.amount > 0;\n  }\n\n  /**\n   * Возвращает отформатированное количество с единицей измерения\n   * @returns {string}\n   */\n  getFormattedQuantity() {\n    return `${this.quantity} ${this.unit}`;\n  }\n\n  /**\n   * Возвращает отформатированную стоимость\n   * @returns {string}\n   */\n  getFormattedAmount() {\n    return `${this.amount} ₽`;\n  }\n\n  /**\n   * Возвращает цену за единицу\n   * @returns {number}\n   */\n  getUnitPrice() {\n    return this.quantity > 0 ? this.amount / this.quantity : 0;\n  }\n}\n\n/**\n * Основная модель заказа\n */\nexport class Order {\n  /**\n   * @param {Object} data - Данные заказа\n   * @param {string} data.id - Уникальный идентификатор заказа\n   * @param {Object} data.user - Данные пользователя\n   * @param {string} data.delivery_type - Тип доставки\n   * @param {string} data.comment - Комментарий к заказу\n   * @param {string} data.address - Адрес доставки\n   * @param {string} data.paymentsystem - Система оплаты\n   * @param {string} data.status - Статус заказа\n   * @param {string|number} data.amount - Общая стоимость заказа\n   * @param {Array} data.meals - Массив блюд в заказе\n   */\n  constructor(data = {}) {\n    this.id = data.id || '';\n    this.user = new OrderUser(data.user || {});\n    this.delivery_type = data.delivery_type || DELIVERY_TYPE.PICKUP;\n    this.comment = data.comment || '';\n    this.address = data.address || '';\n    this.paymentsystem = data.paymentsystem || PAYMENT_SYSTEM.CASH;\n    this.status = data.status || ORDER_STATUS.NEW;\n    this.amount = String(data.amount || '0');\n    this.meals = (data.meals || []).map(meal => new OrderMeal(meal));\n  }\n\n  /**\n   * Проверяет валидность заказа\n   * @returns {boolean}\n   */\n  isValid() {\n    return (\n      this.id !== '' &&\n      this.user.isValid() &&\n      isValidStatus(this.status) &&\n      this.meals.length > 0 &&\n      this.meals.every(meal => meal.isValid())\n    );\n  }\n\n  /**\n   * Возвращает отформатированную общую стоимость\n   * @returns {string}\n   */\n  getFormattedAmount() {\n    return `${this.amount} ₽`;\n  }\n\n  /**\n   * Проверяет, требует ли заказ доставку\n   * @returns {boolean}\n   */\n  isDelivery() {\n    return this.delivery_type === DELIVERY_TYPE.DELIVERY;\n  }\n\n  /**\n   * Проверяет, является ли заказ самовывозом\n   * @returns {boolean}\n   */\n  isPickup() {\n    return this.delivery_type === DELIVERY_TYPE.PICKUP;\n  }\n\n  /**\n   * Возвращает количество блюд в заказе\n   * @returns {number}\n   */\n  getMealsCount() {\n    return this.meals.length;\n  }\n\n  /**\n   * Возвращает общий вес заказа в граммах\n   * @returns {number}\n   */\n  getTotalWeight() {\n    return this.meals.reduce((total, meal) => total + meal.quantity, 0);\n  }\n\n  /**\n   * Возвращает отформатированный общий вес\n   * @returns {string}\n   */\n  getFormattedTotalWeight() {\n    const weight = this.getTotalWeight();\n    if (weight >= 1000) {\n      return `${(weight / 1000).toFixed(1)} кг`;\n    }\n    return `${weight} г`;\n  }\n\n  /**\n   * Проверяет, можно ли изменить статус заказа\n   * @returns {boolean}\n   */\n  canChangeStatus() {\n    return this.status !== ORDER_STATUS.CANCELLED;\n  }\n\n  /**\n   * Проверяет, завершен ли заказ\n   * @returns {boolean}\n   */\n  isCompleted() {\n    return this.status === ORDER_STATUS.COMPLETED || this.status === ORDER_STATUS.DELIVERY;\n  }\n\n  /**\n   * Проверяет, отменен ли заказ\n   * @returns {boolean}\n   */\n  isCancelled() {\n    return this.status === ORDER_STATUS.CANCELLED;\n  }\n\n  /**\n   * Возвращает краткое описание заказа\n   * @returns {string}\n   */\n  getShortDescription() {\n    const mealsCount = this.getMealsCount();\n    const mealsText = mealsCount === 1 ? 'блюдо' : mealsCount < 5 ? 'блюда' : 'блюд';\n    return `${mealsCount} ${mealsText}, ${this.getFormattedTotalWeight()}`;\n  }\n}\n\n/**\n * Фабричная функция для создания заказа из данных\n * @param {Object} data - Данные заказа\n * @returns {Order}\n */\nexport function createOrder(data) {\n  return new Order(data);\n}\n\n/**\n * Проверяет, является ли объект валидным заказом\n * @param {any} obj - Объект для проверки\n * @returns {boolean}\n */\nexport function isValidOrder(obj) {\n  if (!obj || typeof obj !== 'object') return false;\n  \n  try {\n    const order = new Order(obj);\n    return order.isValid();\n  } catch (error) {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAKO,MAAM;IACX;;;;GAIC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU,IAAI;QACrC,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;IAC7B;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO;IAChE;IAEA;;;GAGC,GACD,iBAAiB;QACf,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;;GAGC,GACD,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK;IACnB;AACF;AAKO,MAAM;IACX;;;;;;;;;;;GAWC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,IAAI;QACrB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,QAAQ,KAAK;QACzC,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,MAAM,KAAK;QACrC,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;QAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;IACjC;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,MAAM,GAAG;IAClF;IAEA;;;GAGC,GACD,uBAAuB;QACrB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE;IACxC;IAEA;;;GAGC,GACD,qBAAqB;QACnB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3B;IAEA;;;GAGC,GACD,eAAe;QACb,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG;IAC3D;AACF;AAKO,MAAM;IACX;;;;;;;;;;;GAWC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,IAAI;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,IAAI,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa,IAAI,kIAAA,CAAA,gBAAa,CAAC,MAAM;QAC/D,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;QAC/B,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa,IAAI,kIAAA,CAAA,iBAAc,CAAC,IAAI;QAC9D,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI,+HAAA,CAAA,eAAY,CAAC,GAAG;QAC7C,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,MAAM,IAAI;QACpC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,IAAI,UAAU;IAC5D;IAEA;;;GAGC,GACD,UAAU;QACR,OACE,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,IAAI,CAAC,OAAO,MACjB,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,MAAM,KACzB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA,OAAQ,KAAK,OAAO;IAEzC;IAEA;;;GAGC,GACD,qBAAqB;QACnB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3B;IAEA;;;GAGC,GACD,aAAa;QACX,OAAO,IAAI,CAAC,aAAa,KAAK,kIAAA,CAAA,gBAAa,CAAC,QAAQ;IACtD;IAEA;;;GAGC,GACD,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,KAAK,kIAAA,CAAA,gBAAa,CAAC,MAAM;IACpD;IAEA;;;GAGC,GACD,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IAEA;;;GAGC,GACD,iBAAiB;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IACnE;IAEA;;;GAGC,GACD,0BAA0B;QACxB,MAAM,SAAS,IAAI,CAAC,cAAc;QAClC,IAAI,UAAU,MAAM;YAClB,OAAO,GAAG,CAAC,SAAS,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QAC3C;QACA,OAAO,GAAG,OAAO,EAAE,CAAC;IACtB;IAEA;;;GAGC,GACD,kBAAkB;QAChB,OAAO,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,SAAS;IAC/C;IAEA;;;GAGC,GACD,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,QAAQ;IACxF;IAEA;;;GAGC,GACD,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,SAAS;IAC/C;IAEA;;;GAGC,GACD,sBAAsB;QACpB,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,YAAY,eAAe,IAAI,UAAU,aAAa,IAAI,UAAU;QAC1E,OAAO,GAAG,WAAW,CAAC,EAAE,UAAU,EAAE,EAAE,IAAI,CAAC,uBAAuB,IAAI;IACxE;AACF;AAOO,SAAS,YAAY,IAAI;IAC9B,OAAO,IAAI,MAAM;AACnB;AAOO,SAAS,aAAa,GAAG;IAC9B,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU,OAAO;IAE5C,IAAI;QACF,MAAM,QAAQ,IAAI,MAAM;QACxB,OAAO,MAAM,OAAO;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/page.js"], "sourcesContent": ["import { Section } from \"@/components/layout\";\r\nimport OrderReviewCard from \"@/components/shared/OrderReviewCard\";\r\nimport { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from \"@/components/ui/accordion\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\r\nimport { Clock, CheckCircle, Truck, Package } from \"lucide-react\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\nimport { createOrder } from '@/models/Order';\r\nimport { DELIVERY_TYPE, PAYMENT_SYSTEM } from '@/constants/orderConstants';\r\n\r\nexport default function Home() {\r\n  // Создаем заказ с использованием модели\r\n  const orderData = createOrder({\r\n    id: \"**********\",\r\n    user: {\r\n      first_name: \"<PERSON>а<PERSON><PERSON>\",\r\n      phone: \"+7 (950) 079-32-65\"\r\n    },\r\n    delivery_type: DELIVERY_TYPE.DELIVERY,\r\n    comment: \"Тестовый заказ, не пробовать\",\r\n    address: \"\", // Пустой для самовывоза\r\n    paymentsystem: PAYMENT_SYSTEM.BANK_TRANSFER,\r\n    status: ORDER_STATUS.NEW,\r\n    amount: \"3150\",\r\n    meals: [\r\n      {\r\n        id: \"ehnOzTB06KH0dpL2HiZP\",\r\n        name: \"Окрошка на Квасе\",\r\n        quantity: 1400,\r\n        amount: 1400,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal2\",\r\n        name: \"Борщ украинский\",\r\n        quantity: 700,\r\n        amount: 700,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal3\",\r\n        name: \"Плов узбекский\",\r\n        quantity: 1050,\r\n        amount: 1050,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155502.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      }\r\n    ]\r\n  });\r\n\r\n  const statusSections = [\r\n    {\r\n      id: \"item-1\",\r\n      title: \"Новые заказы\",\r\n      count: 3,\r\n      icon: Clock,\r\n      color: \"text-orange-600\",\r\n      bgColor: \"bg-orange-50\",\r\n      borderColor: \"border-orange-200\"\r\n    },\r\n    {\r\n      id: \"item-2\",\r\n      title: \"Подтверждены\",\r\n      count: 10,\r\n      icon: CheckCircle,\r\n      color: \"text-blue-600\",\r\n      bgColor: \"bg-blue-50\",\r\n      borderColor: \"border-blue-200\"\r\n    },\r\n    {\r\n      id: \"item-3\",\r\n      title: \"Готовы\",\r\n      count: 2,\r\n      icon: Package,\r\n      color: \"text-green-600\",\r\n      bgColor: \"bg-green-50\",\r\n      borderColor: \"border-green-200\"\r\n    },\r\n    {\r\n      id: \"item-4\",\r\n      title: \"В пути\",\r\n      count: 80,\r\n      icon: Truck,\r\n      color: \"text-purple-600\",\r\n      bgColor: \"bg-purple-50\",\r\n      borderColor: \"border-purple-200\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <Section spacing=\"sm\">\r\n      <Accordion\r\n        type=\"multiple\"\r\n        className=\"space-y-3 bg-gray-50 rounded-xl p-4\"\r\n      >\r\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Заказы</h1>\r\n        {statusSections.map((section) => {\r\n          const IconComponent = section.icon;\r\n          return (\r\n            <AccordionItem\r\n              key={section.id}\r\n              value={section.id}\r\n              className=\"bg-white rounded-xl border border-gray-100 overflow-hidden\"\r\n            >\r\n              <AccordionTrigger className=\"text-lg font-semibold text-gray-900 hover:no-underline px-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <IconComponent className={`w-5 h-5 ${section.color}`} />\r\n                    <span>{section.title}</span>\r\n                  </div>\r\n                  <Badge\r\n                    variant=\"secondary\"\r\n                    className={`font-mono ${section.bgColor} ${section.color} border-0 ml-2`}\r\n                  >\r\n                    {section.count}\r\n                  </Badge>\r\n                </div>\r\n              </AccordionTrigger>\r\n              <AccordionContent className=\"px-4\">\r\n                <OrderReviewCard order={orderData} />\r\n                <OrderReviewCard order={orderData} />\r\n                <OrderReviewCard order={orderData} />\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n          );\r\n        })}\r\n      </Accordion>\r\n    </Section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;AAEe,SAAS;IACtB,wCAAwC;IACxC,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI;QACJ,MAAM;YACJ,YAAY;YACZ,OAAO;QACT;QACA,eAAe,kIAAA,CAAA,gBAAa,CAAC,QAAQ;QACrC,SAAS;QACT,SAAS;QACT,eAAe,kIAAA,CAAA,iBAAc,CAAC,aAAa;QAC3C,QAAQ,+HAAA,CAAA,eAAY,CAAC,GAAG;QACxB,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC,6KAAA,CAAA,UAAO;QAAC,SAAQ;kBACf,cAAA,8OAAC,qIAAA,CAAA,YAAS;YACR,MAAK;YACL,WAAU;;8BAEV,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;gBACrD,eAAe,GAAG,CAAC,CAAC;oBACnB,MAAM,gBAAgB,QAAQ,IAAI;oBAClC,qBACE,8OAAC,qIAAA,CAAA,gBAAa;wBAEZ,OAAO,QAAQ,EAAE;wBACjB,WAAU;;0CAEV,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAC1B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAc,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;8DACpD,8OAAC;8DAAM,QAAQ,KAAK;;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAW,CAAC,UAAU,EAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,cAAc,CAAC;sDAEvE,QAAQ,KAAK;;;;;;;;;;;;;;;;;0CAIpB,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;;kDAC1B,8OAAC,+IAAA,CAAA,UAAe;wCAAC,OAAO;;;;;;kDACxB,8OAAC,+IAAA,CAAA,UAAe;wCAAC,OAAO;;;;;;kDACxB,8OAAC,+IAAA,CAAA,UAAe;wCAAC,OAAO;;;;;;;;;;;;;uBArBrB,QAAQ,EAAE;;;;;gBAyBrB;;;;;;;;;;;;AAIR", "debugId": null}}]}