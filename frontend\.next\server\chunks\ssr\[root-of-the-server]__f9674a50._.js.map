{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderConstants.js"], "sourcesContent": ["/**\n * Константы для типов доставки\n */\nexport const DELIVERY_TYPE = {\n  DELIVERY: 'Доставка',\n  PICKUP: 'Самовывоз'\n};\n\n/**\n * Константы для систем оплаты\n */\nexport const PAYMENT_SYSTEM = {\n  BANK_TRANSFER: 'banktransfer',\n  CASH: 'cash',\n  CARD: 'card'\n};\n\n/**\n * Лейблы для систем оплаты\n */\nexport const PAYMENT_LABELS = {\n  [PAYMENT_SYSTEM.BANK_TRANSFER]: 'Банковский перевод',\n  [PAYMENT_SYSTEM.CASH]: 'Наличные',\n  [PAYMENT_SYSTEM.CARD]: 'Банковская карта'\n};\n\n/**\n * Получает лейбл для системы оплаты\n * @param {string} system - Система оплаты\n * @returns {string} - Лейбл системы оплаты\n */\nexport function getPaymentLabel(system) {\n  return PAYMENT_LABELS[system] || system;\n}\n\n/**\n * Проверяет, является ли значение валидным типом доставки\n * @param {string} deliveryType - Тип доставки для проверки\n * @returns {boolean}\n */\nexport function isValidDeliveryType(deliveryType) {\n  return Object.values(DELIVERY_TYPE).includes(deliveryType);\n}\n\n/**\n * Проверяет, является ли значение валидной системой оплаты\n * @param {string} paymentSystem - Система оплаты для проверки\n * @returns {boolean}\n */\nexport function isValidPaymentSystem(paymentSystem) {\n  return Object.values(PAYMENT_SYSTEM).includes(paymentSystem);\n}\n\n/**\n * Возвращает все доступные типы доставки\n * @returns {string[]}\n */\nexport function getAllDeliveryTypes() {\n  return Object.values(DELIVERY_TYPE);\n}\n\n/**\n * Возвращает все доступные системы оплаты\n * @returns {string[]}\n */\nexport function getAllPaymentSystems() {\n  return Object.values(PAYMENT_SYSTEM);\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;AACM,MAAM,gBAAgB;IAC3B,UAAU;IACV,QAAQ;AACV;AAKO,MAAM,iBAAiB;IAC5B,eAAe;IACf,MAAM;IACN,MAAM;AACR;AAKO,MAAM,iBAAiB;IAC5B,CAAC,eAAe,aAAa,CAAC,EAAE;IAChC,CAAC,eAAe,IAAI,CAAC,EAAE;IACvB,CAAC,eAAe,IAAI,CAAC,EAAE;AACzB;AAOO,SAAS,gBAAgB,MAAM;IACpC,OAAO,cAAc,CAAC,OAAO,IAAI;AACnC;AAOO,SAAS,oBAAoB,YAAY;IAC9C,OAAO,OAAO,MAAM,CAAC,eAAe,QAAQ,CAAC;AAC/C;AAOO,SAAS,qBAAqB,aAAa;IAChD,OAAO,OAAO,MAAM,CAAC,gBAAgB,QAAQ,CAAC;AAChD;AAMO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAMO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/paymentLabel.js"], "sourcesContent": ["import { getPaymentLabel } from '@/constants/orderConstants';\r\n\r\nexport default getPaymentLabel;"], "names": [], "mappings": ";;;AAAA;;uCAEe,kIAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderStatus.js"], "sourcesContent": ["/**\n * Enum для статусов заказов\n * Содержит все возможные статусы заказов и их строковые значения\n */\nexport const ORDER_STATUS = {\n  NEW: 'new',\n  CONFIRMED: 'confirmed', \n  COMPLETED: 'completed',\n  DELIVERY: 'delivery',\n  CANCELLED: 'cancelled'\n};\n\n/**\n * Массив статусов в порядке их следования в жизненном цикле заказа\n */\nexport const STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.CANCELLED\n];\n\n/**\n * Проверяет, является ли переданное значение валидным статусом заказа\n * @param {string} status - Статус для проверки\n * @returns {boolean} - true если статус валидный\n */\nexport const isValidStatus = (status) => {\n  return Object.values(ORDER_STATUS).includes(status);\n};\n\n/**\n * Получает все возможные статусы заказов\n * @returns {string[]} - Массив всех статусов\n */\nexport const getAllStatuses = () => {\n  return Object.values(ORDER_STATUS);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AACM,MAAM,eAAe;IAC1B,KAAK;IACL,WAAW;IACX,WAAW;IACX,UAAU;IACV,WAAW;AACb;AAKO,MAAM,eAAe;IAC1B,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAOM,MAAM,gBAAgB,CAAC;IAC5B,OAAO,OAAO,MAAM,CAAC,cAAc,QAAQ,CAAC;AAC9C;AAMO,MAAM,iBAAiB;IAC5B,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock } from \"lucide-react\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        [ORDER_STATUS.NEW]: {\r\n            icon: Clock,\r\n            label: \"Новый\",\r\n            className: \"bg-orange-50 text-orange-600 border-orange-200\"\r\n        },\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            className: \"bg-blue-50 text-blue-600 border-blue-200\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            className: \"bg-green-50 text-green-600 border-green-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В пути\",\r\n            className: \"bg-purple-50 text-purple-600 border-purple-200\"\r\n        },\r\n        [ORDER_STATUS.CANCELLED]: {\r\n            icon: <PERSON><PERSON><PERSON><PERSON>,\r\n            label: \"Отменен\",\r\n            className: \"bg-red-50 text-red-600 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs[ORDER_STATUS.NEW];\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,CAAC,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderCard.jsx"], "sourcesContent": ["import {\r\n  User,\r\n  Phone,\r\n  Truck,\r\n  MessageSquare,\r\n  CreditCard,\r\n  MapPin,\r\n  Package\r\n} from \"lucide-react\";\r\nimport getPaymentLabel from \"@/utils/paymentLabel\";\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderCard({ order }) {\r\n  const statusConfig = getStatusConfig(order.status);\r\n  const StatusIcon = statusConfig.icon;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-8\">\r\n      {/* Заголовок с номером заказа и статусом */}\r\n      <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900\">\r\n            Заказ #{order.id}\r\n          </h3>\r\n          <div className={cn(\r\n            \"inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border\",\r\n            statusConfig.className\r\n          )}>\r\n            <StatusIcon className=\"w-4 h-4\" />\r\n            {statusConfig.label}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-6 space-y-6\">\r\n        {/* Информация о клиенте */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <User className=\"w-5 h-5 text-gray-600\" />\r\n            Информация о клиенте\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4 space-y-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <User className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900 font-medium\">{order.user.first_name}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-3\">\r\n              <Phone className=\"w-4 h-4 text-gray-500\" />\r\n              <a\r\n                href={`tel:${order.user.phone}`}\r\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\r\n              >\r\n                {order.user.phone}\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Детали доставки */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <Truck className=\"w-5 h-5 text-gray-600\" />\r\n            Доставка\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4 space-y-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <MapPin className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900\">{order.delivery_type}</span>\r\n            </div>\r\n            {order.address && (\r\n              <div className=\"flex items-start gap-3\">\r\n                <MapPin className=\"w-4 h-4 text-gray-500 mt-0.5\" />\r\n                <span className=\"text-gray-700\">{order.address}</span>\r\n              </div>\r\n            )}\r\n            {order.comment && (\r\n              <div className=\"flex items-start gap-3\">\r\n                <MessageSquare className=\"w-4 h-4 text-gray-500 mt-0.5\" />\r\n                <span className=\"text-gray-700 italic\">\"{order.comment}\"</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Способ оплаты */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <CreditCard className=\"w-5 h-5 text-gray-600\" />\r\n            Способ оплаты\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <CreditCard className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900\">{getPaymentLabel(order.paymentsystem)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Список блюд */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <Package className=\"w-5 h-5 text-gray-600\" />\r\n            Состав заказа\r\n          </h4>\r\n          <div className=\"space-y-3\">\r\n            {order.meals.map((meal) => (\r\n              <div\r\n                key={meal.id}\r\n                className=\"bg-gray-50 rounded-lg p-3 flex items-center justify-between hover:bg-gray-100 transition-colors\"\r\n              >\r\n                <div className=\"flex-1\">\r\n                  <h5 className=\"font-medium text-gray-900 mb-2\">{meal.name}</h5>\r\n                  <div className=\"flex items-center gap-3 text-sm\">\r\n                    <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\">\r\n                      {Math.floor(meal.quantity / parseInt(meal.portion))} шт\r\n                    </span>\r\n                    <span className=\"text-gray-600\">\r\n                      по {meal.portion} {meal.unit}\r\n                    </span>\r\n                    <span className=\"text-gray-500\">\r\n                      • {meal.quantity} {meal.unit}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-right ml-4\">\r\n                  <div className=\"text-lg font-semibold text-gray-900\">\r\n                    {meal.amount} ₽\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Итоговая сумма */}\r\n        <div className=\"border-t border-gray-200 pt-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-lg font-semibold text-gray-900\">Итого к оплате:</span>\r\n            <span className=\"text-2xl font-bold text-gray-900\">{order.amount} ₽</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;;;;AAEe,SAAS,UAAU,EAAE,KAAK,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCAC1C,MAAM,EAAE;;;;;;;sCAElB,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,sFACA,aAAa,SAAS;;8CAEtB,8OAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;;;;;;0BAKzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAA6B,MAAM,IAAI,CAAC,UAAU;;;;;;;;;;;;kDAEpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDACC,MAAM,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;gDAC/B,WAAU;0DAET,MAAM,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAiB,MAAM,aAAa;;;;;;;;;;;;oCAErD,MAAM,OAAO,kBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAiB,MAAM,OAAO;;;;;;;;;;;;oCAGjD,MAAM,OAAO,kBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;;oDAAuB;oDAAE,MAAM,OAAO;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAiB,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAM1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAkC,KAAK,IAAI;;;;;;kEACzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,SAAS,KAAK,OAAO;oEAAG;;;;;;;0EAEtD,8OAAC;gEAAK,WAAU;;oEAAgB;oEAC1B,KAAK,OAAO;oEAAC;oEAAE,KAAK,IAAI;;;;;;;0EAE9B,8OAAC;gEAAK,WAAU;;oEAAgB;oEAC3B,KAAK,QAAQ;oEAAC;oEAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,MAAM;wDAAC;;;;;;;;;;;;;uCAnBZ,KAAK,EAAE;;;;;;;;;;;;;;;;kCA4BpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;8CACtD,8OAAC;oCAAK,WAAU;;wCAAoC,MAAM,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7E", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/NextStatusButton.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/NextStatusButton.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/NextStatusButton.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/NextStatusButton.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/NextStatusButton.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/NextStatusButton.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/models/Order.js"], "sourcesContent": ["import { ORDER_STATUS, isValidStatus } from '@/constants/orderStatus';\nimport { DELIVERY_TYPE, PAYMENT_SYSTEM, isValidDeliveryType, isValidPaymentSystem } from '@/constants/orderConstants';\n\n/**\n * Модель пользователя заказа\n */\nexport class OrderUser {\n  /**\n   * @param {Object} data - Данные пользователя\n   * @param {string} data.first_name - Имя пользователя\n   * @param {string} data.phone - Телефон пользователя\n   */\n  constructor(data = {}) {\n    this.first_name = data.first_name || '';\n    this.phone = data.phone || '';\n  }\n\n  /**\n   * Проверяет валидность данных пользователя\n   * @returns {boolean}\n   */\n  isValid() {\n    return this.first_name.trim() !== '' && this.phone.trim() !== '';\n  }\n\n  /**\n   * Возвращает полное имя пользователя\n   * @returns {string}\n   */\n  getDisplayName() {\n    return this.first_name.trim();\n  }\n\n  /**\n   * Форматирует телефон для отображения\n   * @returns {string}\n   */\n  getFormattedPhone() {\n    return this.phone;\n  }\n}\n\n/**\n * Модель блюда в заказе\n */\nexport class OrderMeal {\n  /**\n   * @param {Object} data - Данные блюда\n   * @param {string} data.id - Уникальный идентификатор блюда\n   * @param {string} data.name - Название блюда\n   * @param {number} data.quantity - Количество в граммах\n   * @param {number} data.amount - Стоимость\n   * @param {string} data.img - URL изображения\n   * @param {string} data.pack_m - Масса упаковки\n   * @param {string} data.price - Цена за единицу\n   * @param {string} data.unit - Единица измерения\n   * @param {string} data.portion - Размер порции\n   */\n  constructor(data = {}) {\n    this.id = data.id || '';\n    this.name = data.name || '';\n    this.quantity = Number(data.quantity) || 0;\n    this.amount = Number(data.amount) || 0;\n    this.img = data.img || '';\n    this.pack_m = data.pack_m || '';\n    this.price = data.price || '';\n    this.unit = data.unit || 'г';\n    this.portion = data.portion || '';\n  }\n\n  /**\n   * Проверяет валидность данных блюда\n   * @returns {boolean}\n   */\n  isValid() {\n    return this.id !== '' && this.name !== '' && this.quantity > 0 && this.amount > 0;\n  }\n\n  /**\n   * Возвращает отформатированное количество с единицей измерения\n   * @returns {string}\n   */\n  getFormattedQuantity() {\n    return `${this.quantity} ${this.unit}`;\n  }\n\n  /**\n   * Возвращает отформатированную стоимость\n   * @returns {string}\n   */\n  getFormattedAmount() {\n    return `${this.amount} ₽`;\n  }\n\n  /**\n   * Возвращает цену за единицу\n   * @returns {number}\n   */\n  getUnitPrice() {\n    return this.quantity > 0 ? this.amount / this.quantity : 0;\n  }\n}\n\n/**\n * Основная модель заказа\n */\nexport class Order {\n  /**\n   * @param {Object} data - Данные заказа\n   * @param {string} data.id - Уникальный идентификатор заказа\n   * @param {Object} data.user - Данные пользователя\n   * @param {string} data.delivery_type - Тип доставки\n   * @param {string} data.comment - Комментарий к заказу\n   * @param {string} data.address - Адрес доставки\n   * @param {string} data.paymentsystem - Система оплаты\n   * @param {string} data.status - Статус заказа\n   * @param {string|number} data.amount - Общая стоимость заказа\n   * @param {Array} data.meals - Массив блюд в заказе\n   */\n  constructor(data = {}) {\n    this.id = data.id || '';\n    this.user = new OrderUser(data.user || {});\n    this.delivery_type = data.delivery_type || DELIVERY_TYPE.PICKUP;\n    this.comment = data.comment || '';\n    this.address = data.address || '';\n    this.paymentsystem = data.paymentsystem || PAYMENT_SYSTEM.CASH;\n    this.status = data.status || ORDER_STATUS.NEW;\n    this.amount = String(data.amount || '0');\n    this.meals = (data.meals || []).map(meal => new OrderMeal(meal));\n  }\n\n  /**\n   * Проверяет валидность заказа\n   * @returns {boolean}\n   */\n  isValid() {\n    return (\n      this.id !== '' &&\n      this.user.isValid() &&\n      isValidStatus(this.status) &&\n      this.meals.length > 0 &&\n      this.meals.every(meal => meal.isValid())\n    );\n  }\n\n  /**\n   * Возвращает отформатированную общую стоимость\n   * @returns {string}\n   */\n  getFormattedAmount() {\n    return `${this.amount} ₽`;\n  }\n\n  /**\n   * Проверяет, требует ли заказ доставку\n   * @returns {boolean}\n   */\n  isDelivery() {\n    return this.delivery_type === DELIVERY_TYPE.DELIVERY;\n  }\n\n  /**\n   * Проверяет, является ли заказ самовывозом\n   * @returns {boolean}\n   */\n  isPickup() {\n    return this.delivery_type === DELIVERY_TYPE.PICKUP;\n  }\n\n  /**\n   * Возвращает количество блюд в заказе\n   * @returns {number}\n   */\n  getMealsCount() {\n    return this.meals.length;\n  }\n\n  /**\n   * Возвращает общий вес заказа в граммах\n   * @returns {number}\n   */\n  getTotalWeight() {\n    return this.meals.reduce((total, meal) => total + meal.quantity, 0);\n  }\n\n  /**\n   * Возвращает отформатированный общий вес\n   * @returns {string}\n   */\n  getFormattedTotalWeight() {\n    const weight = this.getTotalWeight();\n    if (weight >= 1000) {\n      return `${(weight / 1000).toFixed(1)} кг`;\n    }\n    return `${weight} г`;\n  }\n\n  /**\n   * Проверяет, можно ли изменить статус заказа\n   * @returns {boolean}\n   */\n  canChangeStatus() {\n    return this.status !== ORDER_STATUS.CANCELLED;\n  }\n\n  /**\n   * Проверяет, завершен ли заказ\n   * @returns {boolean}\n   */\n  isCompleted() {\n    return this.status === ORDER_STATUS.COMPLETED || this.status === ORDER_STATUS.DELIVERY;\n  }\n\n  /**\n   * Проверяет, отменен ли заказ\n   * @returns {boolean}\n   */\n  isCancelled() {\n    return this.status === ORDER_STATUS.CANCELLED;\n  }\n\n  /**\n   * Возвращает краткое описание заказа\n   * @returns {string}\n   */\n  getShortDescription() {\n    const mealsCount = this.getMealsCount();\n    const mealsText = mealsCount === 1 ? 'блюдо' : mealsCount < 5 ? 'блюда' : 'блюд';\n    return `${mealsCount} ${mealsText}, ${this.getFormattedTotalWeight()}`;\n  }\n}\n\n/**\n * Фабричная функция для создания заказа из данных\n * @param {Object} data - Данные заказа\n * @returns {Order}\n */\nexport function createOrder(data) {\n  return new Order(data);\n}\n\n/**\n * Проверяет, является ли объект валидным заказом\n * @param {any} obj - Объект для проверки\n * @returns {boolean}\n */\nexport function isValidOrder(obj) {\n  if (!obj || typeof obj !== 'object') return false;\n  \n  try {\n    const order = new Order(obj);\n    return order.isValid();\n  } catch (error) {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAKO,MAAM;IACX;;;;GAIC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU,IAAI;QACrC,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;IAC7B;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO;IAChE;IAEA;;;GAGC,GACD,iBAAiB;QACf,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;;GAGC,GACD,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK;IACnB;AACF;AAKO,MAAM;IACX;;;;;;;;;;;GAWC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,IAAI;QACrB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,QAAQ,KAAK;QACzC,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,MAAM,KAAK;QACrC,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;QAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;IACjC;IAEA;;;GAGC,GACD,UAAU;QACR,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,MAAM,GAAG;IAClF;IAEA;;;GAGC,GACD,uBAAuB;QACrB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE;IACxC;IAEA;;;GAGC,GACD,qBAAqB;QACnB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3B;IAEA;;;GAGC,GACD,eAAe;QACb,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG;IAC3D;AACF;AAKO,MAAM;IACX;;;;;;;;;;;GAWC,GACD,YAAY,OAAO,CAAC,CAAC,CAAE;QACrB,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,IAAI;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,IAAI,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa,IAAI,kIAAA,CAAA,gBAAa,CAAC,MAAM;QAC/D,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;QAC/B,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI;QAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,aAAa,IAAI,kIAAA,CAAA,iBAAc,CAAC,IAAI;QAC9D,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI,+HAAA,CAAA,eAAY,CAAC,GAAG;QAC7C,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,MAAM,IAAI;QACpC,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,IAAI,UAAU;IAC5D;IAEA;;;GAGC,GACD,UAAU;QACR,OACE,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,IAAI,CAAC,OAAO,MACjB,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,MAAM,KACzB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA,OAAQ,KAAK,OAAO;IAEzC;IAEA;;;GAGC,GACD,qBAAqB;QACnB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3B;IAEA;;;GAGC,GACD,aAAa;QACX,OAAO,IAAI,CAAC,aAAa,KAAK,kIAAA,CAAA,gBAAa,CAAC,QAAQ;IACtD;IAEA;;;GAGC,GACD,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,KAAK,kIAAA,CAAA,gBAAa,CAAC,MAAM;IACpD;IAEA;;;GAGC,GACD,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IAEA;;;GAGC,GACD,iBAAiB;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IACnE;IAEA;;;GAGC,GACD,0BAA0B;QACxB,MAAM,SAAS,IAAI,CAAC,cAAc;QAClC,IAAI,UAAU,MAAM;YAClB,OAAO,GAAG,CAAC,SAAS,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QAC3C;QACA,OAAO,GAAG,OAAO,EAAE,CAAC;IACtB;IAEA;;;GAGC,GACD,kBAAkB;QAChB,OAAO,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,SAAS;IAC/C;IAEA;;;GAGC,GACD,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,QAAQ;IACxF;IAEA;;;GAGC,GACD,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,+HAAA,CAAA,eAAY,CAAC,SAAS;IAC/C;IAEA;;;GAGC,GACD,sBAAsB;QACpB,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,YAAY,eAAe,IAAI,UAAU,aAAa,IAAI,UAAU;QAC1E,OAAO,GAAG,WAAW,CAAC,EAAE,UAAU,EAAE,EAAE,IAAI,CAAC,uBAAuB,IAAI;IACxE;AACF;AAOO,SAAS,YAAY,IAAI;IAC9B,OAAO,IAAI,MAAM;AACnB;AAOO,SAAS,aAAa,GAAG;IAC9B,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU,OAAO;IAE5C,IAAI;QACF,MAAM,QAAQ,IAAI,MAAM;QACxB,OAAO,MAAM,OAAO;IACtB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/orders/%5BorderId%5D/page.js"], "sourcesContent": ["import { Section } from \"@/components/layout\";\r\nimport OrderCard from \"@/components/shared/OrderCard\";\r\nimport NextStatusButton from \"@/components/ui/NextStatusButton\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\nimport { createOrder } from '@/models/Order';\r\nimport { DELIVERY_TYPE, PAYMENT_SYSTEM } from '@/constants/orderConstants';\r\n\r\nexport default function Order() {\r\n  // Создаем заказ с использованием модели\r\n  const orderData = createOrder({\r\n    id: \"**********\",\r\n    user: {\r\n      first_name: \"<PERSON>а<PERSON><PERSON>\",\r\n      phone: \"+7 (950) 079-32-65\"\r\n    },\r\n    delivery_type: DELIVERY_TYPE.PICKUP,\r\n    comment: \"Тестовый заказ, не пробовать\",\r\n    address: \"\", // Пустой для самовывоза\r\n    paymentsystem: PAYMENT_SYSTEM.BANK_TRANSFER,\r\n    status: ORDER_STATUS.COMPLETED,\r\n    amount: \"3150\",\r\n    meals: [\r\n      {\r\n        id: \"ehnOzTB06KH0dpL2HiZP\",\r\n        name: \"О<PERSON>рошка на Квасе\",\r\n        quantity: 1400,\r\n        amount: 1400,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal2\",\r\n        name: \"Борщ украинский\",\r\n        quantity: 700,\r\n        amount: 700,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal3\",\r\n        name: \"Плов узбекский\",\r\n        quantity: 1050,\r\n        amount: 1050,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155502.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      }\r\n    ]\r\n  });\r\n\r\n  return (\r\n    <main>\r\n      <Section spacing=\"xs\">\r\n        <OrderCard order={orderData} />\r\n      </Section>\r\n      <NextStatusButton currentStatus={orderData.status}/>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,wCAAwC;IACxC,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI;QACJ,MAAM;YACJ,YAAY;YACZ,OAAO;QACT;QACA,eAAe,kIAAA,CAAA,gBAAa,CAAC,MAAM;QACnC,SAAS;QACT,SAAS;QACT,eAAe,kIAAA,CAAA,iBAAc,CAAC,aAAa;QAC3C,QAAQ,+HAAA,CAAA,eAAY,CAAC,SAAS;QAC9B,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,qBACE,8OAAC;;0BACC,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;0BACf,cAAA,8OAAC,yIAAA,CAAA,UAAS;oBAAC,OAAO;;;;;;;;;;;0BAEpB,8OAAC,4IAAA,CAAA,UAAgB;gBAAC,eAAe,UAAU,MAAM;;;;;;;;;;;;AAGvD", "debugId": null}}]}