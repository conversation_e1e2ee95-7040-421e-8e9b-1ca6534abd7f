{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/services/order.service.js"], "sourcesContent": ["export default class OrderService {\r\n    static async getOrders() {\r\n        orders = await fetch(\"/orders.json\")\r\n        return orders.json()\r\n    }\r\n\r\n    static async getOrdersByStatus(status) {\r\n        orders = await fetch(\"/orders.json\")\r\n        return orders.json().filter(order => order.status === status)\r\n    }\r\n\r\n    static async getOrderById(id) {\r\n        orders = await fetch(\"/orders.json\")\r\n        return orders.json().find(order => order.id === id)\r\n    }\r\n}"], "names": [], "mappings": ";;;AAAe,MAAM;IACjB,aAAa,YAAY;QACrB,SAAS,MAAM,MAAM;QACrB,OAAO,OAAO,IAAI;IACtB;IAEA,aAAa,kBAAkB,MAAM,EAAE;QACnC,SAAS,MAAM,MAAM;QACrB,OAAO,OAAO,IAAI,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAC1D;IAEA,aAAa,aAAa,EAAE,EAAE;QAC1B,SAAS,MAAM,MAAM;QACrB,OAAO,OAAO,IAAI,GAAG,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACpD;AACJ", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/contexts/OrdersContext.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { createContext, useState, useContext } from \"react\";\r\nimport OrderService from \"@/services/order.service\";\r\n\r\nexport const OrdersContext = createContext();\r\n\r\nexport const OrdersProvider = ({ children }) => {\r\n    const [ orders, setOrders ] = useState([])\r\n\r\n    function getOrders() {\r\n        try {\r\n            const orders = OrderService.getOrders()\r\n            return orders\r\n        } catch (error) {\r\n            console.log(error)\r\n        }\r\n    }\r\n\r\n    function getOrdersByStatus(status) {\r\n        try {\r\n            const orders = OrderService.getOrdersByStatus(status)\r\n            return orders\r\n        } catch (error) {\r\n            console.log(error)\r\n        }\r\n    }\r\n\r\n    function getOrderById(id) {\r\n        try {\r\n            const order = OrderService.getOrderById(id)\r\n            return order\r\n        } catch (error) {\r\n            console.log(error)\r\n        }\r\n    }\r\n\r\n    return (\r\n        <OrdersContext.Provider value={{\r\n            orders,\r\n            setOrders,\r\n            getOrders,\r\n            getOrdersByStatus,\r\n            getOrderById\r\n        }}>\r\n            {children}\r\n        </OrdersContext.Provider>\r\n    );\r\n};\r\n\r\nexport function useOrders() {\r\n    return useContext(OrdersContext)\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;AAKO,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;AAElC,MAAM,iBAAiB;QAAC,EAAE,QAAQ,EAAE;;IACvC,MAAM,CAAE,QAAQ,UAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEzC,SAAS;QACL,IAAI;YACA,MAAM,SAAS,sIAAA,CAAA,UAAY,CAAC,SAAS;YACrC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,SAAS,kBAAkB,MAAM;QAC7B,IAAI;YACA,MAAM,SAAS,sIAAA,CAAA,UAAY,CAAC,iBAAiB,CAAC;YAC9C,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,SAAS,aAAa,EAAE;QACpB,IAAI;YACA,MAAM,QAAQ,sIAAA,CAAA,UAAY,CAAC,YAAY,CAAC;YACxC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,qBACI,6LAAC,cAAc,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;QACJ;kBACK;;;;;;AAGb;GAzCa;KAAA;AA2CN,SAAS;;IACZ,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACtB;IAFgB", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/contexts/MainProvider.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { OrdersProvider } from \"./OrdersContext\";\r\n\r\nexport const MainProvider = ({ children }) => {\r\n    return (\r\n        <OrdersProvider>\r\n            {children}\r\n        </OrdersProvider>\r\n    );\r\n};"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE;IACrC,qBACI,6LAAC,oIAAA,CAAA,iBAAc;kBACV;;;;;;AAGb;KANa", "debugId": null}}]}