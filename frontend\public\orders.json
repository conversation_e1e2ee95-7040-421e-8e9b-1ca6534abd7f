[{"id": "**********", "user": {"first_name": "Даня", "phone": "+7 (950) 079-32-65"}, "delivery_type": "Самовывоз", "comment": "Тестовый заказ, не пробовать", "address": "", "paymentsystem": "banktransfer", "status": "completed", "amount": 3150, "meals": [{"id": "ehnOzTB06KH0dpL2HiZP", "name": "Окрошка на Квасе", "quantity": 1400, "amount": 1400, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "1", "unit": "г", "portion": "350"}, {"id": "meal2", "name": "Борщ украинский", "quantity": 700, "amount": 700, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "1", "unit": "г", "portion": "350"}, {"id": "meal3", "name": "Плов узбекский", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "1", "unit": "г", "portion": "350"}]}, {"id": "**********", "user": {"first_name": "<PERSON><PERSON><PERSON>", "phone": "+7 (916) 123-45-67"}, "delivery_type": "Доставка", "comment": "Позвонить за 30 минут до доставки", "address": "ул. <PERSON><PERSON><PERSON><PERSON><PERSON>, д. 15, кв. 42", "paymentsystem": "cash", "status": "completed", "amount": 2450, "meals": [{"id": "meal4", "name": "Лаг<PERSON>ан узбекский", "quantity": 700, "amount": 700, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155503.jpg", "pack_m": "350", "price": "2", "unit": "г", "portion": "350"}, {"id": "meal5", "name": "Манты с мясом", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155504.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal6", "name": "Чай зеленый", "quantity": 700, "amount": 700, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155505.jpg", "pack_m": "200", "price": "3.5", "unit": "мл", "portion": "200"}]}, {"id": "1015547043", "user": {"first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "+7 (903) 567-89-01"}, "delivery_type": "Самовывоз", "comment": "", "address": "", "paymentsystem": "card", "status": "completed", "amount": 1800, "meals": [{"id": "meal7", "name": "Шашлык из баранины", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155506.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal8", "name": "Лепешка узбекская", "quantity": 750, "amount": 750, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "250", "price": "3", "unit": "г", "portion": "250"}]}, {"id": "**********", "user": {"first_name": "Елена", "phone": "+7 (925) 234-56-78"}, "delivery_type": "Доставка", "comment": "Оставить у двери, домофон не работает", "address": "пр. <PERSON><PERSON><PERSON><PERSON>, д. 88, кв. 156", "paymentsystem": "banktransfer", "status": "completed", "amount": 3200, "meals": [{"id": "meal9", "name": "Долма в виноградных листьях", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal10", "name": "Хачапури по-аджарски", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal11", "name": "Компот из сухофруктов", "quantity": 1100, "amount": 1100, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "250", "price": "4.4", "unit": "мл", "portion": "250"}]}, {"id": "**********", "user": {"first_name": "Сергей", "phone": "+7 (985) 345-67-89"}, "delivery_type": "Самовывоз", "comment": "Приеду через час", "address": "", "paymentsystem": "cash", "status": "new", "amount": 1950, "meals": [{"id": "meal12", "name": "Харчо грузинский", "quantity": 700, "amount": 700, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155511.jpg", "pack_m": "350", "price": "2", "unit": "г", "portion": "350"}, {"id": "meal13", "name": "Хинкали с мясом", "quantity": 1250, "amount": 1250, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155512.jpg", "pack_m": "250", "price": "5", "unit": "г", "portion": "250"}]}, {"id": "1015547046", "user": {"first_name": "Ольга", "phone": "+7 (967) 456-78-90"}, "delivery_type": "Доставка", "comment": "Без лука, аллергия", "address": "ул. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, д. 23, кв. 7", "paymentsystem": "card", "status": "new", "amount": 2800, "meals": [{"id": "meal14", "name": "Бешбармак казахский", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155513.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal15", "name": "Самса с тыквой", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155514.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal16", "name": "А<PERSON><PERSON><PERSON><PERSON>", "quantity": 700, "amount": 700, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "250", "price": "2.8", "unit": "мл", "portion": "250"}]}, {"id": "**********", "user": {"first_name": "Д<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "+7 (912) 567-89-12"}, "delivery_type": "Самовывоз", "comment": "Заказ на завтра к 12:00", "address": "", "paymentsystem": "banktransfer", "status": "new", "amount": 4200, "meals": [{"id": "meal17", "name": "Шурпа узбекская", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal18", "name": "Казы по-казахски", "quantity": 1400, "amount": 1400, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "4", "unit": "г", "portion": "350"}, {"id": "meal19", "name": "Баурсаки", "quantity": 1050, "amount": 1050, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "350", "price": "3", "unit": "г", "portion": "350"}, {"id": "meal20", "name": "Ку<PERSON><PERSON>с", "quantity": 700, "amount": 700, "img": "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg", "pack_m": "250", "price": "2.8", "unit": "мл", "portion": "250"}]}]