'use client'

import { createContext, useState, useContext, useEffect } from "react";
import OrderService from "@/services/order.service";

export const OrdersContext = createContext();

export const OrdersProvider = ({ children }) => {
    const [orders, setOrders] = useState([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)

    // Загружаем все заказы при инициализации
    useEffect(() => {
        loadOrders()
    }, [])

    async function loadOrders() {
        setLoading(true)
        setError(null)
        try {
            const ordersData = await OrderService.getOrders()
            setOrders(ordersData)
        } catch (error) {
            console.error('Ошибка при загрузке заказов:', error)
            setError(error.message)
        } finally {
            setLoading(false)
        }
    }

    async function getOrders() {
        try {
            return await OrderService.getOrders()
        } catch (error) {
            console.error('Ошибка при получении заказов:', error)
            setError(error.message)
            return []
        }
    }

    async function getOrdersByStatus(status) {
        try {
            return await OrderService.getOrdersByStatus(status)
        } catch (error) {
            console.error('Ошибка при получении заказов по статусу:', error)
            setError(error.message)
            return []
        }
    }

    // Синхронная функция для получения заказов по статусу из уже загруженных данных
    function getOrdersByStatusSync(status) {
        return orders.filter(order => order.status === status)
    }

    async function getOrderById(id) {
        try {
            return await OrderService.getOrderById(id)
        } catch (error) {
            console.error('Ошибка при получении заказа по ID:', error)
            setError(error.message)
            return null
        }
    }

    return (
        <OrdersContext.Provider value={{
            orders,
            setOrders,
            loading,
            error,
            getOrders,
            getOrdersByStatus,
            getOrdersByStatusSync,
            getOrderById,
            loadOrders
        }}>
            {children}
        </OrdersContext.Provider>
    );
};

export function useOrders() {
    return useContext(OrdersContext)
}