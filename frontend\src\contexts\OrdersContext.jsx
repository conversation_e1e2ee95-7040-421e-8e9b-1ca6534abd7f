'use client'

import { createContext, useState, useContext } from "react";
import OrderService from "@/services/order.service";

export const OrdersContext = createContext();

export const OrdersProvider = ({ children }) => {
    const [ orders, setOrders ] = useState([])

    function getOrders() {
        try {
            const orders = OrderService.getOrders()
            return orders
        } catch (error) {
            console.log(error)
        }
    }

    function getOrdersByStatus(status) {
        try {
            const orders = OrderService.getOrdersByStatus(status)
            return orders
        } catch (error) {
            console.log(error)
        }
    }

    function getOrderById(id) {
        try {
            const order = OrderService.getOrderById(id)
            return order
        } catch (error) {
            console.log(error)
        }
    }

    return (
        <OrdersContext.Provider value={{
            orders,
            setOrders,
            getOrders,
            getOrdersByStatus,
            getOrderById
        }}>
            {children}
        </OrdersContext.Provider>
    );
};

export function useOrders() {
    return useContext(OrdersContext)
}