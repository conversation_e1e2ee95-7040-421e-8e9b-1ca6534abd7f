(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/order.service.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>OrderService
});
class OrderService {
    static async getOrders() {
        try {
            const response = await fetch("/orders.json");
            if (!response.ok) {
                throw new Error("HTTP error! status: ".concat(response.status));
            }
            const orders = await response.json();
            return orders;
        } catch (error) {
            console.error('Ошибка при загрузке заказов:', error);
            throw error;
        }
    }
    static async getOrdersByStatus(status) {
        try {
            const response = await fetch("/orders.json");
            if (!response.ok) {
                throw new Error("HTTP error! status: ".concat(response.status));
            }
            const orders = await response.json();
            return orders.filter((order)=>order.status === status);
        } catch (error) {
            console.error('Ошибка при загрузке заказов по статусу:', error);
            throw error;
        }
    }
    static async getOrderById(id) {
        try {
            const response = await fetch("/orders.json");
            if (!response.ok) {
                throw new Error("HTTP error! status: ".concat(response.status));
            }
            const orders = await response.json();
            return orders.find((order)=>order.id === id);
        } catch (error) {
            console.error('Ошибка при загрузке заказа по ID:', error);
            throw error;
        }
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/OrdersContext.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "OrdersContext": ()=>OrdersContext,
    "OrdersProvider": ()=>OrdersProvider,
    "useOrders": ()=>useOrders
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$order$2e$service$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/order.service.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const OrdersContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])();
const OrdersProvider = (param)=>{
    let { children } = param;
    _s();
    const [orders, setOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Загружаем все заказы при инициализации
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OrdersProvider.useEffect": ()=>{
            loadOrders();
        }
    }["OrdersProvider.useEffect"], []);
    async function loadOrders() {
        setLoading(true);
        setError(null);
        try {
            const ordersData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$order$2e$service$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getOrders();
            setOrders(ordersData);
        } catch (error) {
            console.error('Ошибка при загрузке заказов:', error);
            setError(error.message);
        } finally{
            setLoading(false);
        }
    }
    async function getOrders() {
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$order$2e$service$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getOrders();
        } catch (error) {
            console.error('Ошибка при получении заказов:', error);
            setError(error.message);
            return [];
        }
    }
    async function getOrdersByStatus(status) {
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$order$2e$service$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getOrdersByStatus(status);
        } catch (error) {
            console.error('Ошибка при получении заказов по статусу:', error);
            setError(error.message);
            return [];
        }
    }
    // Синхронная функция для получения заказов по статусу из уже загруженных данных
    function getOrdersByStatusSync(status) {
        return orders.filter((order)=>order.status === status);
    }
    async function getOrderById(id) {
        try {
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$order$2e$service$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getOrderById(id);
        } catch (error) {
            console.error('Ошибка при получении заказа по ID:', error);
            setError(error.message);
            return null;
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(OrdersContext.Provider, {
        value: {
            orders,
            setOrders,
            loading,
            error,
            getOrders,
            getOrdersByStatus,
            getOrdersByStatusSync,
            getOrderById,
            loadOrders
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/OrdersContext.jsx",
        lineNumber: 68,
        columnNumber: 9
    }, ("TURBOPACK compile-time value", void 0));
};
_s(OrdersProvider, "NvRvzRo6fN35U/te/Upk6+qZnyE=");
_c = OrdersProvider;
function useOrders() {
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(OrdersContext);
}
_s1(useOrders, "gDsCjeeItUuvgOWf1v4qoK9RF6k=");
var _c;
__turbopack_context__.k.register(_c, "OrdersProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/MainProvider.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MainProvider": ()=>MainProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/OrdersContext.jsx [app-client] (ecmascript)");
'use client';
;
;
const MainProvider = (param)=>{
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OrdersProvider"], {
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/MainProvider.jsx",
        lineNumber: 7,
        columnNumber: 9
    }, ("TURBOPACK compile-time value", void 0));
};
_c = MainProvider;
var _c;
__turbopack_context__.k.register(_c, "MainProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_0b5e9705._.js.map