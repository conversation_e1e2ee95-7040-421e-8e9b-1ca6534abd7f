export default class OrderService {
    static async getOrders() {
        orders = await fetch("/orders.json")
        return orders.json()
    }

    static async getOrdersByStatus(status) {
        orders = await fetch("/orders.json")
        return orders.json().filter(order => order.status === status)
    }

    static async getOrderById(id) {
        orders = await fetch("/orders.json")
        return orders.json().find(order => order.id === id)
    }
}