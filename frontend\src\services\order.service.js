export default class OrderService {
    static async getOrders() {
        try {
            const response = await fetch("/orders.json")
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            const orders = await response.json()
            return orders
        } catch (error) {
            console.error('Ошибка при загрузке заказов:', error)
            throw error
        }
    }

    static async getOrdersByStatus(status) {
        try {
            const response = await fetch("/orders.json")
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            const orders = await response.json()
            return orders.filter(order => order.status === status)
        } catch (error) {
            console.error('Ошибка при загрузке заказов по статусу:', error)
            throw error
        }
    }

    static async getOrderById(id) {
        try {
            const response = await fetch("/orders.json")
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            const orders = await response.json()
            return orders.find(order => order.id === id)
        } catch (error) {
            console.error('Ошибка при загрузке заказа по ID:', error)
            throw error
        }
    }
}